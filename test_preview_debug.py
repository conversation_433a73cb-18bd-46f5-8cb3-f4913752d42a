#!/usr/bin/env python3
"""
Debug script to test preview functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import tempfile
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class PreviewTestWindow:
    def __init__(self, parent=None):
        # Create window like ProjectOverview does
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("Preview Test")
        self.window.geometry("800x600")

        # Make modal like ProjectOverview
        if parent:
            self.window.transient(parent)
            self.window.grab_set()

        # Initialize preview_photo
        self.preview_photo = None

        # Create UI
        self.setup_ui()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill="both", expand=True)

        # Test button
        test_btn = ttk.Button(main_frame, text="Test Preview Loading",
                             command=self.test_preview)
        test_btn.pack(pady=10)

        # Canvas setup exactly like ProjectOverview
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="5")
        preview_frame.pack(fill="both", expand=True, pady=(0, 10))

        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill="both", expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg="white", relief="sunken", bd=1)

        # Scrollbars like ProjectOverview
        v_scroll_preview = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scroll_preview = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scroll_preview.set, xscrollcommand=h_scroll_preview.set)

        # Grid layout like ProjectOverview
        self.preview_canvas.grid(row=0, column=0, sticky="nsew")
        v_scroll_preview.grid(row=0, column=1, sticky="ns")
        h_scroll_preview.grid(row=1, column=0, sticky="ew")

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.pack(pady=5)
        
    def test_preview(self):
        """Test preview loading with a simple test image"""
        try:
            if not PIL_AVAILABLE:
                self.status_label.config(text="PIL not available")
                return
                
            # Create a simple test image
            from PIL import Image, ImageDraw
            
            # Create a test image
            test_image = Image.new('RGB', (200, 150), color='lightblue')
            draw = ImageDraw.Draw(test_image)
            draw.text((50, 70), "Test Image", fill='black')
            
            # Save to temp file
            temp_file = os.path.join(tempfile.gettempdir(), "test_preview.png")
            test_image.save(temp_file)
            
            # Now try to load and display it
            self.load_test_image(temp_file)
            
        except Exception as e:
            self.status_label.config(text=f"Error creating test image: {e}")
            
    def load_test_image(self, image_path):
        """Load and display test image"""
        try:
            self.status_label.config(text="Loading image...")
            
            # Clear canvas
            self.preview_canvas.delete("all")
            
            # Load image
            image = Image.open(image_path)
            
            # Resize if needed
            max_size = (400, 300)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Method 1: Direct assignment (like DataAnalysisApp.py)
            self.preview_photo = ImageTk.PhotoImage(image)
            self.preview_canvas.create_image(10, 10, anchor="nw", image=self.preview_photo)
            
            # Update canvas scroll region
            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
            
            self.status_label.config(text="Image loaded successfully!")
            
        except Exception as e:
            self.status_label.config(text=f"Error loading image: {e}")
            self.preview_canvas.create_text(10, 10, anchor="nw", 
                                          text=f"Could not load image: {e}")

def main():
    app = PreviewTestWindow()
    app.window.mainloop()

if __name__ == "__main__":
    main()
