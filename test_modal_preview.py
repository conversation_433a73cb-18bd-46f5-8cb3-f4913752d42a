#!/usr/bin/env python3
"""
Test modal window preview functionality to debug the pyimage issue
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import tempfile
try:
    from PIL import Image, ImageTk, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Main Window")
        self.root.geometry("400x300")
        
        # Button to open modal window
        open_btn = ttk.Button(self.root, text="Open Modal Preview Window", 
                             command=self.open_modal)
        open_btn.pack(pady=50)
        
    def open_modal(self):
        """Open modal preview window like ProjectOverview does"""
        # Hide main window like DataAnalysisApp does
        self.root.withdraw()
        
        # Create modal window
        ModalPreviewWindow(self.root, self)

class ModalPreviewWindow:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.preview_photo = None
        
        # Create window exactly like ProjectOverview
        self.window = tk.Toplevel(parent)
        self.window.title("Modal Preview Test")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Handle window closing
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # Test button
        test_btn = ttk.Button(main_frame, text="Load Test Image", 
                             command=self.load_test_image)
        test_btn.pack(pady=10)
        
        # Preview frame exactly like ProjectOverview
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="5")
        preview_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Canvas frame with scrollbars
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill="both", expand=True)
        
        self.preview_canvas = tk.Canvas(canvas_frame, bg="white", relief="sunken", bd=1)
        
        # Scrollbars
        v_scroll = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scroll = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        # Grid layout like ProjectOverview
        self.preview_canvas.grid(row=0, column=0, sticky="nsew")
        v_scroll.grid(row=0, column=1, sticky="ns")
        h_scroll.grid(row=1, column=0, sticky="ew")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready - Click button to test")
        self.status_label.pack(pady=5)
        
    def load_test_image(self):
        """Load test image using exact ProjectOverview method"""
        try:
            if not PIL_AVAILABLE:
                self.status_label.config(text="PIL not available")
                return
                
            # Create test image
            test_image = Image.new('RGB', (300, 200), color='lightgreen')
            draw = ImageDraw.Draw(test_image)
            draw.text((50, 90), "Modal Test Image", fill='black')
            draw.rectangle([10, 10, 290, 190], outline='blue', width=3)
            
            # Save to temp file
            temp_file = os.path.join(tempfile.gettempdir(), "modal_test_preview.png")
            test_image.save(temp_file)
            
            # Load and display using ProjectOverview method
            self.load_preview_image(temp_file)
            
        except Exception as e:
            self.status_label.config(text=f"Error: {e}")
            
    def load_preview_image(self, image_path):
        """Load preview image using exact ProjectOverview code"""
        try:
            # Clear previous preview
            self.preview_canvas.delete("all")
            
            if os.path.exists(image_path):
                if PIL_AVAILABLE:
                    from PIL import Image, ImageTk
                    
                    # Load and display image
                    image = Image.open(image_path)
                    
                    # Resize if too large
                    max_size = (400, 300)
                    image.thumbnail(max_size, Image.Resampling.LANCZOS)
                    
                    # Create PhotoImage and assign to instance variable immediately
                    self.preview_photo = ImageTk.PhotoImage(image)
                    self.preview_canvas.create_image(10, 10, anchor="nw", image=self.preview_photo)
                    
                    # Update canvas scroll region
                    self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
                    
                    self.status_label.config(text="Image loaded successfully!")
                else:
                    self.preview_canvas.create_text(10, 10, anchor="nw",
                                                  text="PIL not available - cannot display image")
            else:
                self.preview_canvas.create_text(10, 10, anchor="nw",
                                              text="No preview available")
                                              
        except Exception as e:
            self.status_label.config(text=f"Error loading image: {e}")
            self.preview_canvas.create_text(10, 10, anchor="nw",
                                          text=f"Could not load preview: {e}")
            
    def on_closing(self):
        """Handle window closing like ProjectOverview"""
        # Show main app window
        if self.main_app and hasattr(self.main_app, 'root'):
            self.main_app.root.deiconify()
        
        self.window.destroy()

def main():
    app = MainWindow()
    app.root.mainloop()

if __name__ == "__main__":
    main()
