"""
3D Plotting Window - Adapted from plot3D.py
Recreates the original plot3D interface but works with data from ImportWizard
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import json
import os
import datetime
from pathlib import Path

# Project directory configuration
DEFAULT_PROJECT_DIR = os.path.join(os.path.expanduser("~"), "Documents", "PlottingApp_Projects")
APP_CONFIG_FILE = "app_config.json"

def get_project_directory():
    """Get the project directory from config, with fallback to default"""
    try:
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                return config.get('project_directory', DEFAULT_PROJECT_DIR)
    except Exception:
        pass
    return DEFAULT_PROJECT_DIR

def save_project_directory(directory):
    """Save the project directory to config file"""
    try:
        config = {}
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, 'r') as f:
                config = json.load(f)

        config['project_directory'] = directory

        with open(APP_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        return True
    except Exception as e:
        print(f"Failed to save config: {e}")
        return False

def check_directory_permissions(directory):
    """Check if directory is accessible for read/write operations"""
    try:
        # Check if directory exists and is accessible
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        # Test write permissions by creating a temporary file
        test_file = os.path.join(directory, ".test_write_permission")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        return True
    except Exception:
        return False

def setup_project_directory():
    """Setup project directory with user selection"""
    root = tk.Tk()
    root.withdraw()  # Hide the root window

    # Check if default directory is accessible
    if check_directory_permissions(DEFAULT_PROJECT_DIR):
        result = messagebox.askyesno(
            "Project Directory Setup",
            f"Use default project directory?\n\n{DEFAULT_PROJECT_DIR}\n\n"
            "Click 'No' to select a different directory."
        )
        if result:
            save_project_directory(DEFAULT_PROJECT_DIR)
            root.destroy()
            return DEFAULT_PROJECT_DIR

    # Let user select directory
    selected_dir = filedialog.askdirectory(
        title="Select Project Directory",
        initialdir=os.path.expanduser("~")
    )

    if selected_dir and check_directory_permissions(selected_dir):
        save_project_directory(selected_dir)
        root.destroy()
        return selected_dir
    elif selected_dir:
        messagebox.showerror(
            "Directory Error",
            f"Cannot access selected directory:\n{selected_dir}\n\n"
            "Please select a different directory or check permissions."
        )
        root.destroy()
        return setup_project_directory()  # Retry
    else:
        root.destroy()
        return None  # User cancelled

class Measurement3D:
    """Data structure for 3D measurements - compatible with original plot3D"""
    def __init__(self, name, x, y, z_dict, dimension=3):
        self.name = name
        self.x = x  # 1D array
        self.y = y  # 1D array
        self.z_signals = {}  # Dictionary of Z data
        self.dimension = dimension
        
        # Convert z_dict to the expected format
        for signal_name, z_data in z_dict.items():
            self.z_signals[signal_name] = {
                "data": z_data,
                "enabled": True
            }
    
    def get_meshgrid(self):
        """Get X, Y meshgrid for 3D plotting"""
        return np.meshgrid(self.x, self.y)

class Plot3DWindow:
    """3D Plotting Window - Recreated from plot3D.py"""
    
    def __init__(self, master, data, config, project_callback=None):
        self.master = master
        self.data = data
        self.config = config
        self.project_callback = project_callback
        self.measurements = []
        self.measurement_items = {}
        self.signal_check_vars = {}

        # Setup project directory from config or fallback to setup
        if 'project_directory' in self.config:
            self.project_base_dir = self.config['project_directory']
            # Verify the directory is accessible
            if not check_directory_permissions(self.project_base_dir):
                messagebox.showwarning(
                    "Project Directory Access",
                    f"Cannot access project directory:\n{self.project_base_dir}\n\n"
                    "Please check the directory permissions in the main application settings."
                )
                # Use a fallback directory for this session
                self.project_base_dir = DEFAULT_PROJECT_DIR
        else:
            # Fallback to original setup if no config provided
            self.project_base_dir = setup_project_directory()
            if self.project_base_dir is None:
                # User cancelled directory selection, close the window
                self.master.destroy()
                return

        self.master.title("3D Plot Window")
        self.master.geometry("1400x900")
        
        # Initialize plotting variables
        self.ax = None
        self.fig = None
        self.canvas = None
        self.colorbar = None  # Track colorbar to avoid duplicates
        
        self.init_plot_ui()
        self.load_data_from_import()
        self.draw_plot()
    
    def init_plot_ui(self):
        """Initialize the plotting UI - exact replica of plot3D layout"""
        self.frame = ttk.Frame(self.master)
        self.frame.pack(fill="both", expand=True)
        
        # Left frame for controls
        left_frame = ttk.Frame(self.frame)
        left_frame.pack(side="left", fill="y", padx=5, pady=5)
        
        # Measurement tree
        treeview_frame = ttk.Frame(left_frame)
        treeview_frame.pack(fill="both", expand=True)

        # Add instruction label
        instruction_label = ttk.Label(treeview_frame,
                                    text="💡 Double-click measurement names to rename",
                                    font=("Arial", 9),
                                    foreground="gray")
        instruction_label.pack(anchor="w", pady=(0, 5))
        
        self.measurement_tree = ttk.Treeview(
            treeview_frame,
            columns=('enabled',),
            show='tree headings',
            selectmode='browse'
        )
        self.measurement_tree.heading('#0', text='Measurement / Signal')
        self.measurement_tree.heading('enabled', text='Plot', command=lambda: self.toggle_all_signals())
        self.measurement_tree.column('#0', width=180, anchor='w')
        self.measurement_tree.column('enabled', width=50, anchor='center')
        self.measurement_tree.pack(side="left", fill="both", expand=True)
        
        # Scrollbar for tree
        scrollbar = ttk.Scrollbar(treeview_frame, orient="vertical", command=self.measurement_tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.measurement_tree.configure(yscrollcommand=scrollbar.set)
        
        # Bind tree click events
        self.measurement_tree.bind('<ButtonRelease-1>', self.handle_tree_click)
        self.measurement_tree.bind('<Double-1>', self.handle_tree_double_click)
        
        # Right frame for plot and controls
        right_frame = ttk.Frame(self.frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # Create matplotlib figure with 3D projection
        self.fig = plt.figure(figsize=(10, 8))
        self.create_axes()  # Create initial axes
        self.canvas = FigureCanvasTkAgg(self.fig, master=right_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # Navigation toolbar
        toolbar = NavigationToolbar2Tk(self.canvas, right_frame)
        toolbar.update()
        self.canvas._tkcanvas.pack(fill="both", expand=True)

        # Create tabbed interface for controls
        self.notebook = ttk.Notebook(right_frame)
        self.notebook.pack(fill="both", expand=True, pady=5)

        # Plotting tab
        self.plotting_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.plotting_tab, text="Plotting")

        # Editing tab
        self.editing_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.editing_tab, text="Editing")

        # Setup control panels in plotting tab
        self.setup_control_panels(self.plotting_tab)

        # Setup editing controls in editing tab
        self.setup_editing_controls(self.editing_tab)
    
    def setup_control_panels(self, parent):
        """Setup all control panels exactly like plot3D"""
        # Plot Style Frame
        self.plot_style_frame = ttk.LabelFrame(parent, text="Plot Style")
        self.plot_style_frame.pack(fill="x", padx=5, pady=3)
        
        # View Control Frame
        self.view_frame = ttk.LabelFrame(parent, text="View Control")
        self.view_frame.pack(fill="x", padx=5, pady=3)
        
        # Axis Control Frame
        self.axis_frame = ttk.LabelFrame(parent, text="Axis Control")
        self.axis_frame.pack(fill="x", padx=5, pady=3)
        
        # Font Style Frame
        self.font_frame = ttk.LabelFrame(parent, text="Font Style")
        self.font_frame.pack(fill="x", padx=5, pady=3)
        
        # Control Panel Frame
        self.control_panel = ttk.LabelFrame(parent, text="Control Panel")
        self.control_panel.pack(fill="x", padx=5, pady=3)
        
        # Initialize control variables
        self.init_control_variables()
        
        # Setup individual control panels
        self.setup_plot_style_controls()
        self.setup_view_controls()
        self.setup_axis_controls()
        self.setup_font_controls()
        self.setup_control_buttons()
    
    def init_control_variables(self):
        """Initialize all control variables"""
        # Plot style variables
        self.plot_type_var = tk.StringVar(value="contour")
        self.colormap_var = tk.StringVar(value="viridis")
        self.alpha_var = tk.StringVar(value="1.0")
        self.linewidth_var = tk.StringVar(value="1.0")
        self.colorbar_var = tk.BooleanVar(value=True)
        self.label_mode_var = tk.StringVar(value="both")
        
        # View control variables
        self.elev_var = tk.StringVar(value="30")
        self.azim_var = tk.StringVar(value="45")
        
        # Axis variables
        self.log_x_var = tk.BooleanVar(value=False)
        self.log_y_var = tk.BooleanVar(value=False)
        self.log_z_var = tk.BooleanVar(value=False)
        
        # Font variables
        self.font_var = tk.StringVar(value="Arial")
        self.fontsize_var = tk.StringVar(value="12")
        self.ticksize_var = tk.StringVar(value="10")
    
    def setup_plot_style_controls(self):
        """Setup plot style control panel"""
        # Plot type
        ttk.Label(self.plot_style_frame, text="Plot Type:").pack(side="left", padx=(5, 0))
        plot_type_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.plot_type_var,
                                      values=["contour", "surface", "scatter", "wireframe"],
                                      state="readonly", width=10)
        plot_type_combo.pack(side="left", padx=(0, 5))
        
        # Colormap
        ttk.Label(self.plot_style_frame, text="Colormap:").pack(side="left", padx=(5, 0))
        colormap_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.colormap_var,
                                     values=["viridis", "plasma", "inferno", "magma", "cividis", 
                                            "jet", "hot", "cool", "spring", "summer", "autumn", "winter",
                                            "gray", "bone", "copper", "pink", "flag", "prism", "ocean",
                                            "gist_earth", "terrain", "gist_stern", "gnuplot", "gnuplot2",
                                            "CMRmap", "cubehelix", "brg", "gist_rainbow", "rainbow",
                                            "nipy_spectral", "gist_ncar"],
                                     state="readonly", width=12)
        colormap_combo.pack(side="left", padx=(0, 5))
        
        # Alpha
        ttk.Label(self.plot_style_frame, text="Alpha:").pack(side="left", padx=(5, 0))
        self.alpha_entry = ttk.Entry(self.plot_style_frame, textvariable=self.alpha_var, width=5)
        self.alpha_entry.pack(side="left", padx=(0, 5))
        
        # Line width
        ttk.Label(self.plot_style_frame, text="Line Width:").pack(side="left", padx=(5, 0))
        self.linewidth_entry = ttk.Entry(self.plot_style_frame, textvariable=self.linewidth_var, width=5)
        self.linewidth_entry.pack(side="left", padx=(0, 5))
        
        # Colorbar
        ttk.Checkbutton(self.plot_style_frame, text="Colorbar", variable=self.colorbar_var).pack(side="left", padx=5)

        # Label mode
        ttk.Label(self.plot_style_frame, text="Labels:").pack(side="left", padx=(5, 0))
        label_mode_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.label_mode_var,
                                       values=["file", "header", "both"], state="readonly", width=8)
        label_mode_combo.pack(side="left", padx=(0, 5))
        label_mode_combo.bind('<<ComboboxSelected>>', lambda e: self.draw_plot())
    
    def setup_view_controls(self):
        """Setup view control panel"""
        # Elevation
        ttk.Label(self.view_frame, text="Elevation:").pack(side="left", padx=(5, 0))
        self.elev_entry = ttk.Entry(self.view_frame, textvariable=self.elev_var, width=5)
        self.elev_entry.pack(side="left", padx=(0, 5))
        
        # Azimuth
        ttk.Label(self.view_frame, text="Azimuth:").pack(side="left", padx=(5, 0))
        self.azim_entry = ttk.Entry(self.view_frame, textvariable=self.azim_var, width=5)
        self.azim_entry.pack(side="left", padx=(0, 5))
        
        # View buttons
        ttk.Button(self.view_frame, text="Top View", command=lambda: self.set_view(90, 0)).pack(side="left", padx=2)
        ttk.Button(self.view_frame, text="Side View", command=lambda: self.set_view(0, 0)).pack(side="left", padx=2)
        ttk.Button(self.view_frame, text="Front View", command=lambda: self.set_view(0, 90)).pack(side="left", padx=2)
        ttk.Button(self.view_frame, text="3D View", command=lambda: self.set_view(30, 45)).pack(side="left", padx=2)

    def setup_editing_controls(self, parent):
        """Setup editing controls in the editing tab"""
        # Create scrollable frame for editing controls
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Data Processing Frame
        processing_frame = ttk.LabelFrame(scrollable_frame, text="Data Processing")
        processing_frame.pack(fill="x", padx=5, pady=3)

        # Row 1: Basic operations
        row1 = ttk.Frame(processing_frame)
        row1.pack(fill="x", padx=5, pady=2)
        ttk.Button(row1, text="Normalize", command=self.normalize_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Smooth", command=self.smooth_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Crop", command=self.crop_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Shift", command=self.shift_data).pack(side="left", padx=2)

        # Row 2: Advanced operations
        row2 = ttk.Frame(processing_frame)
        row2.pack(fill="x", padx=5, pady=2)
        ttk.Button(row2, text="FFT", command=self.apply_fft).pack(side="left", padx=2)
        ttk.Button(row2, text="Filter", command=self.filter_data).pack(side="left", padx=2)
        ttk.Button(row2, text="Baseline", command=self.baseline_correction).pack(side="left", padx=2)
        ttk.Button(row2, text="Interpolate", command=self.interpolate_data).pack(side="left", padx=2)

        # Data Analysis Frame
        analysis_frame = ttk.LabelFrame(scrollable_frame, text="Data Analysis")
        analysis_frame.pack(fill="x", padx=5, pady=3)

        # Row 3: Analysis operations
        row3 = ttk.Frame(analysis_frame)
        row3.pack(fill="x", padx=5, pady=2)
        ttk.Button(row3, text="Statistics", command=self.show_statistics).pack(side="left", padx=2)
        ttk.Button(row3, text="Peak Find", command=self.find_peaks).pack(side="left", padx=2)
        ttk.Button(row3, text="Integrate", command=self.integrate_data).pack(side="left", padx=2)
        ttk.Button(row3, text="Fit Surface", command=self.fit_surface).pack(side="left", padx=2)

        # Data Export Frame
        export_frame = ttk.LabelFrame(scrollable_frame, text="Data Export")
        export_frame.pack(fill="x", padx=5, pady=3)

        # Row 4: Export operations
        row4 = ttk.Frame(export_frame)
        row4.pack(fill="x", padx=5, pady=2)
        ttk.Button(row4, text="Export Data", command=self.export_data).pack(side="left", padx=2)
        ttk.Button(row4, text="Export Plot", command=self.export_plot).pack(side="left", padx=2)
        ttk.Button(row4, text="Save Config", command=self.save_config).pack(side="left", padx=2)
        ttk.Button(row4, text="Load Config", command=self.load_config).pack(side="left", padx=2)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_axes(self):
        """Create or recreate the 3D axes"""
        # Clear the entire figure
        self.fig.clear()

        # Create new 3D axes
        self.ax = self.fig.add_subplot(111, projection='3d')

        # Reset colorbar reference
        self.colorbar = None

    def load_data_from_import(self):
        """Load data from ImportWizard format and convert to 3D measurements"""
        if self.data is None or self.data.empty:
            return

        # Check if we have multiple files to load
        exported_files = self.config.get('exported_files', [])
        export_directory = self.config.get('export_directory', '')

        if len(exported_files) > 1 and export_directory:
            # Load multiple files
            self.load_multiple_files(exported_files, export_directory)
        else:
            # Load single file (original behavior)
            self.load_single_file()

        # Populate measurement tree
        self.populate_measurement_tree()

    def load_multiple_files(self, exported_files, export_directory):
        """Load multiple exported files as separate 3D measurements"""
        for file_name in exported_files:
            try:
                file_path = os.path.join(export_directory, file_name)
                file_data = pd.read_csv(file_path, comment='#')

                if file_data.empty:
                    continue

                # Extract measurement name from filename (remove extension and dimension suffix)
                measurement_name = Path(file_name).stem
                # Remove dimension suffix if present (e.g., "_1D", "_2D", "_3D")
                if measurement_name.endswith(('_1D', '_2D', '_3D')):
                    measurement_name = measurement_name[:-3]

                # Convert to 3D Measurement object
                self.load_3d_data_from_dataframe(file_data, measurement_name)

            except Exception as e:
                print(f"Warning: Failed to load 3D file {file_name}: {e}")
                continue

    def load_single_file(self):
        """Load single file data (original behavior)"""
        measurement_name = getattr(self, 'measurement_name', 'Imported 3D Data')
        self.load_3d_data_from_dataframe(self.data, measurement_name)

    def load_3d_data_from_dataframe(self, data, measurement_name):
        """Load 3D data from a DataFrame"""
        try:
            # For 3D data, assume first row contains X coordinates, first column contains Y coordinates
            # Remaining data is Z values

            # Extract X coordinates from first row (excluding first cell)
            x_data = data.iloc[0, 1:].values.astype(float)

            # Extract Y coordinates from first column (excluding first cell)
            y_data = data.iloc[1:, 0].values.astype(float)

            # Extract Z data (excluding first row and first column)
            z_matrix = data.iloc[1:, 1:].values.astype(float)

            # Create z_dict with the matrix as a single signal
            z_dict = {"Z_data": z_matrix}

            # Create measurement object
            measurement = Measurement3D(measurement_name, x_data, y_data, z_dict)
            self.measurements.append(measurement)

        except Exception as e:
            print(f"Warning: Failed to load 3D data for {measurement_name}: {str(e)}")
    
    def populate_measurement_tree(self):
        """Populate the measurement tree with data"""
        # Clear existing tree
        for item in self.measurement_tree.get_children():
            self.measurement_tree.delete(item)
        
        self.measurement_items.clear()
        self.signal_check_vars.clear()
        
        # Add measurements to tree
        for measurement in self.measurements:
            # Add measurement as parent item
            measurement_item = self.measurement_tree.insert('', 'end', text=measurement.name, values=('',))
            self.measurement_items[measurement_item] = measurement
            
            # Add signals as child items
            for signal_name, signal_info in measurement.z_signals.items():
                enabled_var = tk.BooleanVar(value=signal_info["enabled"])
                self.signal_check_vars[(measurement.name, signal_name)] = enabled_var

                signal_item = self.measurement_tree.insert(
                    measurement_item, 'end',
                    text=signal_name,
                    values=('✅' if enabled_var.get() else '⬜️',)
                )

    def setup_axis_controls(self):
        """Setup axis control panel"""
        # X Axis
        ttk.Label(self.axis_frame, text="X Label:").pack(side="left", padx=(5, 0))
        self.x_label_entry = ttk.Entry(self.axis_frame, width=8)
        self.x_label_entry.insert(0, self.config.get("x_label", "X Axis"))
        self.x_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="X min:").pack(side="left", padx=(5, 0))
        self.x_min_entry = ttk.Entry(self.axis_frame, width=6)
        self.x_min_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="X max:").pack(side="left", padx=(5, 0))
        self.x_max_entry = ttk.Entry(self.axis_frame, width=6)
        self.x_max_entry.pack(side="left", padx=(0, 5))

        ttk.Checkbutton(self.axis_frame, text="Log X", variable=self.log_x_var).pack(side="left", padx=2)

        # Y Axis
        ttk.Label(self.axis_frame, text="Y Label:").pack(side="left", padx=(5, 0))
        self.y_label_entry = ttk.Entry(self.axis_frame, width=8)
        self.y_label_entry.insert(0, self.config.get("y_label", "Y Axis"))
        self.y_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="Y min:").pack(side="left", padx=(5, 0))
        self.y_min_entry = ttk.Entry(self.axis_frame, width=6)
        self.y_min_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="Y max:").pack(side="left", padx=(5, 0))
        self.y_max_entry = ttk.Entry(self.axis_frame, width=6)
        self.y_max_entry.pack(side="left", padx=(0, 5))

        ttk.Checkbutton(self.axis_frame, text="Log Y", variable=self.log_y_var).pack(side="left", padx=2)

        # Z Axis
        ttk.Label(self.axis_frame, text="Z Label:").pack(side="left", padx=(5, 0))
        self.z_label_entry = ttk.Entry(self.axis_frame, width=8)
        self.z_label_entry.insert(0, self.config.get("z_label", "Z Axis"))
        self.z_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="Z min:").pack(side="left", padx=(5, 0))
        self.z_min_entry = ttk.Entry(self.axis_frame, width=6)
        self.z_min_entry.pack(side="left", padx=(0, 5))

        ttk.Label(self.axis_frame, text="Z max:").pack(side="left", padx=(5, 0))
        self.z_max_entry = ttk.Entry(self.axis_frame, width=6)
        self.z_max_entry.pack(side="left", padx=(0, 5))

        ttk.Checkbutton(self.axis_frame, text="Log Z", variable=self.log_z_var).pack(side="left", padx=2)

    def setup_font_controls(self):
        """Setup font control panel"""
        # Title
        ttk.Label(self.font_frame, text="Title:").pack(side="left", padx=(5, 0))
        self.title_entry = ttk.Entry(self.font_frame, width=15)
        self.title_entry.insert(0, self.config.get("title", ""))
        self.title_entry.pack(side="left", padx=(0, 5))

        # Font
        ttk.Label(self.font_frame, text="Font:").pack(side="left", padx=(5, 0))
        self.font_entry = ttk.Entry(self.font_frame, textvariable=self.font_var, width=8)
        self.font_entry.pack(side="left", padx=(0, 5))

        # Font size
        ttk.Label(self.font_frame, text="Font Size:").pack(side="left", padx=(5, 0))
        self.fontsize_entry = ttk.Entry(self.font_frame, textvariable=self.fontsize_var, width=5)
        self.fontsize_entry.pack(side="left", padx=(0, 5))

        # Tick size
        ttk.Label(self.font_frame, text="Tick Size:").pack(side="left", padx=(5, 0))
        self.ticksize_entry = ttk.Entry(self.font_frame, textvariable=self.ticksize_var, width=5)
        self.ticksize_entry.pack(side="left", padx=(0, 5))

    def setup_control_buttons(self):
        """Setup control buttons"""
        ttk.Button(self.control_panel, text="Update Plot", command=self.draw_plot).pack(side="left", padx=5)
        ttk.Button(self.control_panel, text="Save Project", command=self.save_project).pack(side="left", padx=5)
        ttk.Button(self.control_panel, text="Save Config", command=self.save_config).pack(side="left", padx=5)
        ttk.Button(self.control_panel, text="Load Config", command=self.load_config).pack(side="left", padx=5)
        ttk.Button(self.control_panel, text="Export Project", command=self.export_project).pack(side="left", padx=5)

    def handle_tree_click(self, event):
        """Handle clicks on the measurement tree"""
        region = self.measurement_tree.identify_region(event.x, event.y)
        column = self.measurement_tree.identify_column(event.x)
        row = self.measurement_tree.identify_row(event.y)

        if not row or column != '#1':
            return

        item = self.measurement_tree.item(row)
        parent_id = self.measurement_tree.parent(row)

        if parent_id:  # Signal level
            measurement_name = self.measurement_tree.item(parent_id)['text']
            signal_label = item['text']

            # Toggle Enabled
            var = self.signal_check_vars.get((measurement_name, signal_label))
            if var:
                var.set(not var.get())
                self.measurement_tree.set(row, 'enabled', '✅' if var.get() else '⬜️')

            self.draw_plot()

    def handle_tree_double_click(self, event):
        """Handle double-click on measurement tree for renaming"""
        row = self.measurement_tree.identify_row(event.y)
        if not row:
            return

        item = self.measurement_tree.item(row)
        parent_id = self.measurement_tree.parent(row)

        # Only allow renaming of measurement names (parent items), not signals
        if not parent_id:  # This is a measurement (parent item)
            current_name = item['text']
            measurement = self.measurement_items.get(row)
            if measurement:
                self.show_measurement_rename_dialog(measurement, current_name)

    def show_measurement_rename_dialog(self, measurement, current_name):
        """Show dialog for renaming measurement"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Rename Measurement")
        dialog.geometry("400x150")
        dialog.resizable(False, False)
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill="both", expand=True)

        # Current name label
        ttk.Label(main_frame, text=f"Current name: {current_name}").pack(anchor="w", pady=(0, 10))

        # New name entry
        ttk.Label(main_frame, text="New name:").pack(anchor="w")
        name_var = tk.StringVar(value=current_name)
        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=40)
        name_entry.pack(fill="x", pady=(5, 15))
        name_entry.select_range(0, tk.END)
        name_entry.focus()

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        def on_rename():
            new_name = name_var.get().strip()
            if not new_name:
                messagebox.showerror("Error", "Measurement name cannot be empty")
                return

            if new_name == current_name:
                dialog.destroy()
                return

            # Check for duplicate names
            existing_names = [m.name for m in self.measurements if m != measurement]
            if new_name in existing_names:
                messagebox.showerror("Error", f"A measurement named '{new_name}' already exists")
                return

            # Perform the rename
            self.perform_measurement_rename(measurement, new_name)
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Rename", command=on_rename).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right")

        # Bind Enter key to rename
        dialog.bind('<Return>', lambda e: on_rename())
        dialog.bind('<Escape>', lambda e: on_cancel())

    def perform_measurement_rename(self, measurement, new_name):
        """Perform the actual measurement rename"""
        old_name = measurement.name

        # Update measurement name
        measurement.name = new_name

        # Update signal check vars with new measurement name
        for (meas_name, signal_name), var in list(self.signal_check_vars.items()):
            if meas_name == old_name:
                del self.signal_check_vars[(meas_name, signal_name)]
                self.signal_check_vars[(new_name, signal_name)] = var

        # Refresh the tree and plot
        self.populate_measurement_tree()
        self.draw_plot()

        # Show success message in status (if available)
        print(f"Measurement renamed from '{old_name}' to '{new_name}'")

    def toggle_all_signals(self):
        """Toggle all signals on/off"""
        # Check if any signal is currently enabled
        any_enabled = any(var.get() for var in self.signal_check_vars.values())
        new_state = not any_enabled

        # Update all signal variables and tree display
        for (measurement_name, signal_name), var in self.signal_check_vars.items():
            var.set(new_state)
            # Find the tree item and update display
            for measurement_item in self.measurement_tree.get_children():
                if self.measurement_tree.item(measurement_item)['text'] == measurement_name:
                    for signal_item in self.measurement_tree.get_children(measurement_item):
                        if self.measurement_tree.item(signal_item)['text'] == signal_name:
                            self.measurement_tree.set(signal_item, 'enabled', '✅' if new_state else '⬜️')

        self.draw_plot()

    def set_view(self, elev, azim):
        """Set view angle"""
        self.elev_var.set(str(elev))
        self.azim_var.set(str(azim))
        self.ax.view_init(elev=elev, azim=azim)
        self.canvas.draw()

    # Helper methods for 3D processing
    def get_selected_z_signals(self):
        """Get list of selected Z signals from treeview"""
        selected_signals = []
        for measurement in self.measurements:
            for z_name in measurement.z_signals:
                item_id = f"{measurement.name}_{z_name}"
                if item_id in self.measurement_items and self.tree.set(self.measurement_items[item_id], "Select") == "✓":
                    selected_signals.append((measurement.name, z_name))
        return selected_signals

    def get_normalize_method_3d(self):
        """Get normalization method from user for 3D data"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Normalize 3D Data")
        dialog.geometry("300x200")
        dialog.transient(self.master)
        dialog.grab_set()

        method_var = tk.StringVar(value="min_max")
        result = [None]

        ttk.Label(dialog, text="Normalization Method:").pack(pady=10)
        methods = [("Min-Max", "min_max"), ("Z-Score", "z_score")]
        for text, value in methods:
            ttk.Radiobutton(dialog, text=text, variable=method_var, value=value).pack(anchor='w', padx=20)

        def ok_clicked():
            result[0] = method_var.get()
            dialog.destroy()

        def cancel_clicked():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        ttk.Button(button_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_clicked).pack(side='left', padx=5)

        dialog.wait_window()
        return result[0]

    # Editing methods (implemented for 3D data)
    def normalize_data(self):
        """Normalize 3D data values"""
        try:
            # Get selected Z signals
            selected_signals = self.get_selected_z_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select Z signals to normalize")
                return

            # Get normalization method from user
            method = self.get_normalize_method_3d()
            if not method:
                return

            # Process each selected Z signal
            for measurement_name, z_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Normalize Z data
                z_data = measurement.z_signals[z_name]
                if method == 'min_max':
                    z_min, z_max = np.min(z_data), np.max(z_data)
                    normalized_z = (z_data - z_min) / (z_max - z_min)
                elif method == 'z_score':
                    normalized_z = (z_data - np.mean(z_data)) / np.std(z_data)

                # Update measurement
                measurement.z_signals[z_name] = normalized_z

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Normalized {len(selected_signals)} Z signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"3D normalization failed: {str(e)}")

    def smooth_data(self):
        """Apply smoothing to 3D data"""
        try:
            from scipy.ndimage import gaussian_filter

            # Get selected Z signals
            selected_signals = self.get_selected_z_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select Z signals to smooth")
                return

            # Get sigma value from user
            sigma = simpledialog.askfloat("Smooth Data", "Enter Gaussian sigma value:", initialvalue=1.0)
            if sigma is None:
                return

            # Process each selected Z signal
            for measurement_name, z_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Apply Gaussian smoothing to 2D Z data
                smoothed_z = gaussian_filter(measurement.z_signals[z_name], sigma=sigma)

                # Update measurement
                measurement.z_signals[z_name] = smoothed_z

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Smoothed {len(selected_signals)} Z signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"3D smoothing failed: {str(e)}")

    def crop_data(self):
        """Crop 3D data to selected region"""
        messagebox.showinfo("Crop", "3D data cropping - Interactive selection coming soon")

    def shift_data(self):
        """Shift 3D data values"""
        try:
            # Get selected Z signals
            selected_signals = self.get_selected_z_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select Z signals to shift")
                return

            # Get shift value from user
            shift_value = simpledialog.askfloat("Shift Data", "Enter shift value:", initialvalue=0.0)
            if shift_value is None:
                return

            # Process each selected Z signal
            for measurement_name, z_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Shift Z data
                measurement.z_signals[z_name] = measurement.z_signals[z_name] + shift_value

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Shifted {len(selected_signals)} Z signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"3D shifting failed: {str(e)}")

    def apply_fft(self):
        """Apply FFT transformation to 3D data"""
        messagebox.showinfo("FFT", "3D FFT transformation - Advanced feature coming soon")

    def filter_data(self):
        """Apply data filtering to 3D data"""
        messagebox.showinfo("Filter", "3D data filtering - Advanced feature coming soon")

    def baseline_correction(self):
        """Apply baseline correction to 3D data"""
        messagebox.showinfo("Baseline", "3D baseline correction - Advanced feature coming soon")

    def interpolate_data(self):
        """Interpolate data"""
        messagebox.showinfo("Interpolate", "Data interpolation functionality - to be implemented")

    def show_statistics(self):
        """Show data statistics"""
        messagebox.showinfo("Statistics", "Data statistics functionality - to be implemented")

    def find_peaks(self):
        """Find peaks in data"""
        messagebox.showinfo("Peak Find", "Peak finding functionality - to be implemented")

    def integrate_data(self):
        """Integrate data"""
        messagebox.showinfo("Integrate", "Data integration functionality - to be implemented")

    def fit_surface(self):
        """Fit surface to data"""
        messagebox.showinfo("Fit Surface", "Surface fitting functionality - to be implemented")

    def export_data(self):
        """Export processed data"""
        messagebox.showinfo("Export Data", "Data export functionality - to be implemented")

    def export_plot(self):
        """Export plot as image"""
        messagebox.showinfo("Export Plot", "Plot export functionality - to be implemented")

    def save_config(self):
        """Save current configuration"""
        messagebox.showinfo("Save Config", "Configuration saving functionality - to be implemented")

    def load_config(self):
        """Load configuration"""
        messagebox.showinfo("Load Config", "Configuration loading functionality - to be implemented")

    def draw_plot(self):
        """Main plotting function - recreated from plot3D"""
        # Recreate axes to ensure proper layout
        self.create_axes()

        # Get style settings
        plot_type = self.plot_type_var.get()
        colormap = self.colormap_var.get()

        try:
            alpha = float(self.alpha_var.get())
            linewidth = float(self.linewidth_var.get())
            elev = float(self.elev_var.get())
            azim = float(self.azim_var.get())
        except ValueError:
            alpha = 1.0
            linewidth = 1.0
            elev = 30
            azim = 45

        # Plot enabled signals
        for measurement in self.measurements:
            for signal_name, signal_info in measurement.z_signals.items():
                # Get the checkbox value for this signal
                enabled_var = self.signal_check_vars.get((measurement.name, signal_name))
                if enabled_var and enabled_var.get():
                    # Get data
                    X, Y = measurement.get_meshgrid()
                    Z = signal_info["data"]

                    # Ensure Z has the right shape for meshgrid
                    if Z.shape != X.shape:
                        Z = Z.T  # Transpose if needed

                    # Plot based on type
                    mappable = None  # Track the mappable object for colorbar

                    if plot_type == "surface":
                        mappable = self.ax.plot_surface(X, Y, Z, cmap=colormap, alpha=alpha, linewidth=linewidth)
                    elif plot_type == "wireframe":
                        self.ax.plot_wireframe(X, Y, Z, cmap=colormap, alpha=alpha, linewidth=linewidth)
                    elif plot_type == "contour":
                        mappable = self.ax.contour(X, Y, Z, cmap=colormap, alpha=alpha, linewidths=linewidth)
                    elif plot_type == "scatter":
                        # Flatten arrays for scatter plot
                        x_flat = X.flatten()
                        y_flat = Y.flatten()
                        z_flat = Z.flatten()
                        mappable = self.ax.scatter(x_flat, y_flat, z_flat, c=z_flat, cmap=colormap, alpha=alpha)

                    # Add colorbar only once, for the last plotted item with mappable
                    if mappable is not None and self.colorbar_var.get() and self.colorbar is None:
                        self.colorbar = self.fig.colorbar(mappable, ax=self.ax, shrink=0.5, aspect=5)

        # Apply styling
        self.apply_plot_styling()

        # Set view angle
        self.ax.view_init(elev=elev, azim=azim)

        # Refresh canvas
        self.canvas.draw()
        self.canvas.flush_events()

    def apply_plot_styling(self):
        """Apply styling to the plot"""
        # Title
        title = self.title_entry.get()
        if title:
            self.ax.set_title(title, fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())

        # Axis labels
        self.ax.set_xlabel(self.x_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())
        self.ax.set_ylabel(self.y_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())
        self.ax.set_zlabel(self.z_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())

        # Tick sizes
        tick_size = int(self.ticksize_var.get())
        self.ax.tick_params(labelsize=tick_size)

        # Axis limits
        try:
            x_min = float(self.x_min_entry.get()) if self.x_min_entry.get() else None
            x_max = float(self.x_max_entry.get()) if self.x_max_entry.get() else None
            if x_min is not None or x_max is not None:
                self.ax.set_xlim(x_min, x_max)
        except ValueError:
            pass

        try:
            y_min = float(self.y_min_entry.get()) if self.y_min_entry.get() else None
            y_max = float(self.y_max_entry.get()) if self.y_max_entry.get() else None
            if y_min is not None or y_max is not None:
                self.ax.set_ylim(y_min, y_max)
        except ValueError:
            pass

        try:
            z_min = float(self.z_min_entry.get()) if self.z_min_entry.get() else None
            z_max = float(self.z_max_entry.get()) if self.z_max_entry.get() else None
            if z_min is not None or z_max is not None:
                self.ax.set_zlim(z_min, z_max)
        except ValueError:
            pass

        # Log scales
        if self.log_x_var.get():
            self.ax.set_xscale("log")
        if self.log_y_var.get():
            self.ax.set_yscale("log")
        if self.log_z_var.get():
            self.ax.set_zscale("log")

    def save_config(self):
        """Save current configuration to JSON file"""
        config_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")]
        )
        if config_path:
            config = {
                "x_label": self.x_label_entry.get(),
                "y_label": self.y_label_entry.get(),
                "z_label": self.z_label_entry.get(),
                "title": self.title_entry.get(),
                "x_lim": [self.x_min_entry.get(), self.x_max_entry.get()],
                "y_lim": [self.y_min_entry.get(), self.y_max_entry.get()],
                "z_lim": [self.z_min_entry.get(), self.z_max_entry.get()],
                "plot_type": self.plot_type_var.get(),
                "colormap": self.colormap_var.get(),
                "alpha": float(self.alpha_var.get()),
                "linewidth": float(self.linewidth_var.get()),
                "colorbar": self.colorbar_var.get(),
                "elevation": float(self.elev_var.get()),
                "azimuth": float(self.azim_var.get()),
                "font": self.font_var.get(),
                "fontsize": int(self.fontsize_var.get()),
                "ticksize": int(self.ticksize_var.get()),
                "log_x": self.log_x_var.get(),
                "log_y": self.log_y_var.get(),
                "log_z": self.log_z_var.get()
            }

            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)
            messagebox.showinfo("Success", f"Configuration saved to {config_path}")

    def load_config(self):
        """Load configuration from JSON file"""
        config_path = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json")]
        )
        if config_path:
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)

                # Apply loaded configuration
                self.x_label_entry.delete(0, tk.END)
                self.x_label_entry.insert(0, config.get("x_label", ""))
                self.y_label_entry.delete(0, tk.END)
                self.y_label_entry.insert(0, config.get("y_label", ""))
                self.z_label_entry.delete(0, tk.END)
                self.z_label_entry.insert(0, config.get("z_label", ""))
                self.title_entry.delete(0, tk.END)
                self.title_entry.insert(0, config.get("title", ""))

                # Axis limits
                x_lim = config.get("x_lim", ["", ""])
                self.x_min_entry.delete(0, tk.END)
                self.x_min_entry.insert(0, x_lim[0])
                self.x_max_entry.delete(0, tk.END)
                self.x_max_entry.insert(0, x_lim[1])

                y_lim = config.get("y_lim", ["", ""])
                self.y_min_entry.delete(0, tk.END)
                self.y_min_entry.insert(0, y_lim[0])
                self.y_max_entry.delete(0, tk.END)
                self.y_max_entry.insert(0, y_lim[1])

                z_lim = config.get("z_lim", ["", ""])
                self.z_min_entry.delete(0, tk.END)
                self.z_min_entry.insert(0, z_lim[0])
                self.z_max_entry.delete(0, tk.END)
                self.z_max_entry.insert(0, z_lim[1])

                # Other settings
                self.plot_type_var.set(config.get("plot_type", "contour"))
                self.colormap_var.set(config.get("colormap", "viridis"))
                self.alpha_var.set(str(config.get("alpha", 1.0)))
                self.linewidth_var.set(str(config.get("linewidth", 1.0)))
                self.colorbar_var.set(config.get("colorbar", True))
                self.elev_var.set(str(config.get("elevation", 30)))
                self.azim_var.set(str(config.get("azimuth", 45)))
                self.font_var.set(config.get("font", "Arial"))
                self.fontsize_var.set(str(config.get("fontsize", 12)))
                self.ticksize_var.set(str(config.get("ticksize", 10)))
                self.log_x_var.set(config.get("log_x", False))
                self.log_y_var.set(config.get("log_y", False))
                self.log_z_var.set(config.get("log_z", False))

                # Redraw plot
                self.draw_plot()
                messagebox.showinfo("Success", f"Configuration loaded from {config_path}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {str(e)}")

    def save_project(self):
        """Save project to structured directory"""
        if not check_directory_permissions(self.project_base_dir):
            messagebox.showerror("Directory Error",
                               f"Cannot access project directory:\n{self.project_base_dir}\n\n"
                               "Please restart the application to select a new directory.")
            return

        # Ensure project base directory exists
        os.makedirs(self.project_base_dir, exist_ok=True)

        # Step 1: Select or create project
        project_name = self.select_or_create_project()
        if not project_name:
            return

        # Step 2: Get measurement name
        measurement_name = self.get_measurement_name()
        if not measurement_name:
            return

        try:
            # Save all project data
            self.save_project_data(project_name, measurement_name)

            messagebox.showinfo("Success",
                              f"Project saved successfully!\n\n"
                              f"Project: {project_name}\n"
                              f"Measurement: {measurement_name}\n"
                              f"Location: {os.path.join(self.project_base_dir, project_name, measurement_name)}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save project: {str(e)}")

    def select_or_create_project(self):
        """Select existing project or create new one"""
        # Get list of existing projects
        existing_projects = []
        if os.path.exists(self.project_base_dir):
            existing_projects = [d for d in os.listdir(self.project_base_dir)
                               if os.path.isdir(os.path.join(self.project_base_dir, d))]

        # Create dialog
        dialog = tk.Toplevel(self.master)
        dialog.title("Select or Create Project")
        dialog.geometry("400x300")
        dialog.transient(self.master)
        dialog.grab_set()

        result = {"project": None}

        # Project selection frame
        ttk.Label(dialog, text="Select existing project or create new:").pack(pady=10)

        # Listbox for existing projects
        if existing_projects:
            ttk.Label(dialog, text="Existing Projects:").pack(anchor="w", padx=20)
            project_listbox = tk.Listbox(dialog, height=6)
            project_listbox.pack(fill="both", expand=True, padx=20, pady=5)

            for project in existing_projects:
                project_listbox.insert(tk.END, project)

        # New project entry
        ttk.Label(dialog, text="Or create new project:").pack(anchor="w", padx=20, pady=(10, 0))
        new_project_entry = ttk.Entry(dialog, width=40)
        new_project_entry.pack(padx=20, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def on_ok():
            # Check if new project name is provided
            new_name = new_project_entry.get().strip()
            if new_name:
                result["project"] = new_name
            elif existing_projects and project_listbox.curselection():
                selected_idx = project_listbox.curselection()[0]
                result["project"] = existing_projects[selected_idx]
            else:
                messagebox.showwarning("Selection Required", "Please select a project or enter a new project name.")
                return
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="left", padx=5)

        # Wait for dialog to close
        dialog.wait_window()

        return result["project"]

    def get_measurement_name(self):
        """Get measurement name from user"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Measurement Name")
        dialog.geometry("350x150")
        dialog.transient(self.master)
        dialog.grab_set()

        result = {"measurement": None}

        ttk.Label(dialog, text="Enter measurement name:").pack(pady=10)

        measurement_entry = ttk.Entry(dialog, width=40)
        measurement_entry.pack(pady=5)
        measurement_entry.focus()

        # Default name suggestion
        default_name = f"3D_Measurement_{len(self.measurements)}"
        measurement_entry.insert(0, default_name)
        measurement_entry.select_range(0, tk.END)

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        def on_ok():
            name = measurement_entry.get().strip()
            if name:
                result["measurement"] = name
                dialog.destroy()
            else:
                messagebox.showwarning("Name Required", "Please enter a measurement name.")

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="left", padx=5)

        # Bind Enter key
        dialog.bind('<Return>', lambda e: on_ok())

        # Wait for dialog to close
        dialog.wait_window()

        return result["measurement"]

    def export_project(self):
        """Export project data and configuration"""
        folder = filedialog.askdirectory(title="Select Export Folder")
        if not folder:
            return

        base_name = "3d_plot_project"

        try:
            # Save plot as image
            plot_path = os.path.join(folder, "plot.png")
            self.fig.savefig(plot_path, dpi=300, bbox_inches='tight')

            # Save configuration
            config_path = os.path.join(folder, f"{base_name}_config.json")
            config = {
                "x_label": self.x_label_entry.get(),
                "y_label": self.y_label_entry.get(),
                "z_label": self.z_label_entry.get(),
                "title": self.title_entry.get(),
                "plot_type": self.plot_type_var.get(),
                "colormap": self.colormap_var.get(),
                "alpha": float(self.alpha_var.get()),
                "linewidth": float(self.linewidth_var.get()),
                "colorbar": self.colorbar_var.get(),
                "elevation": float(self.elev_var.get()),
                "azimuth": float(self.azim_var.get()),
                "font": self.font_var.get(),
                "fontsize": int(self.fontsize_var.get()),
                "ticksize": int(self.ticksize_var.get())
            }

            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)

            # Save measurement data
            for measurement in self.measurements:
                # Save X, Y coordinates
                coords_filename = f"{base_name}_{measurement.name}_coordinates.csv"
                coords_filepath = os.path.join(folder, coords_filename)
                coords_df = pd.DataFrame({
                    "X": measurement.x,
                    "Y": measurement.y
                })
                coords_df.to_csv(coords_filepath, index=False)

                # Save Z data for each signal
                for signal_name, signal_info in measurement.z_signals.items():
                    z_filename = f"{base_name}_{measurement.name}_{signal_name}.csv"
                    z_filepath = os.path.join(folder, z_filename)
                    z_df = pd.DataFrame(signal_info["data"])
                    z_df.to_csv(z_filepath, index=False)

            messagebox.showinfo("Export Complete", f"Project exported to {folder}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export project: {str(e)}")

    def save_project_data(self, project_name, measurement_name):
        """Save all project data to the structured directory"""
        # Create directory structure
        project_path = os.path.join(self.project_base_dir, project_name)
        measurement_path = os.path.join(project_path, measurement_name)
        data_path = os.path.join(measurement_path, "Data")

        os.makedirs(data_path, exist_ok=True)

        # 1. Save plot preview
        preview_filename = "preview.png"
        preview_path = os.path.join(measurement_path, preview_filename)
        try:
            self.fig.savefig(preview_path, dpi=300, bbox_inches='tight')
            print(f"Preview saved successfully: {preview_path}")
        except Exception as e:
            print(f"Error saving preview: {e}")
            raise

        # 2. Save plot configuration
        config_path = os.path.join(measurement_path, "plot_config.json")
        config = {
            "x_label": self.x_label_entry.get(),
            "y_label": self.y_label_entry.get(),
            "z_label": self.z_label_entry.get(),
            "title": self.title_entry.get(),
            "plot_type": self.plot_type_var.get(),
            "colormap": self.colormap_var.get(),
            "alpha": float(self.alpha_var.get()),
            "linewidth": float(self.linewidth_var.get()),
            "colorbar": self.colorbar_var.get(),
            "elevation": float(self.elev_var.get()),
            "azimuth": float(self.azim_var.get()),
            "font": self.font_var.get(),
            "fontsize": int(self.fontsize_var.get()),
            "ticksize": int(self.ticksize_var.get()),
            "log_x": getattr(self, 'log_x_var', tk.BooleanVar()).get(),
            "log_y": getattr(self, 'log_y_var', tk.BooleanVar()).get(),
            "log_z": getattr(self, 'log_z_var', tk.BooleanVar()).get()
        }

        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)

        # 3. Save metadata
        metadata = {
            "project_name": project_name,
            "measurement_name": measurement_name,
            "created_date": datetime.datetime.now().isoformat(),
            "plot_type": "3D",
            "data_files": [],
            "measurement_count": len(self.measurements),
            "signal_count": sum(len(m.z_signals) for m in self.measurements)
        }

        # 4. Save measurement data
        for measurement in self.measurements:
            # Save coordinates and Z data combined
            filename = f"{measurement.name}.csv"
            filepath = os.path.join(data_path, filename)

            # Create combined dataframe with X, Y coordinates and all Z signals
            combined_data = pd.DataFrame({
                "X": measurement.x,
                "Y": measurement.y
            })

            # Add each Z signal as a column
            for signal_name, signal_info in measurement.z_signals.items():
                # Flatten the Z data if it's 2D
                z_data = signal_info["data"]
                if isinstance(z_data, np.ndarray) and z_data.ndim > 1:
                    z_data = z_data.flatten()
                combined_data[f"Z_{signal_name}"] = z_data

            combined_data.to_csv(filepath, index=False)
            metadata["data_files"].append(filename)

        # Save metadata
        metadata_path = os.path.join(measurement_path, "metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=4)

        # 5. Save processing log
        processing_log = {
            "created_date": datetime.datetime.now().isoformat(),
            "operations": [
                {
                    "timestamp": datetime.datetime.now().isoformat(),
                    "operation": "project_save",
                    "details": f"Saved 3D plot to project '{project_name}', measurement '{measurement_name}'"
                }
            ]
        }

        log_path = os.path.join(measurement_path, "processing_log.json")
        with open(log_path, "w") as f:
            json.dump(processing_log, f, indent=4)

# Test function
def test_3d_plotting():
    """Test function for 3D plotting window"""
    root = tk.Tk()
    root.withdraw()  # Hide main window

    # Create sample 3D data
    x = np.linspace(-5, 5, 20)
    y = np.linspace(-5, 5, 20)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(np.sqrt(X**2 + Y**2))

    # Create data in ImportWizard format (first row = X, first col = Y, rest = Z)
    data_matrix = np.zeros((len(y) + 1, len(x) + 1))
    data_matrix[0, 1:] = x  # X coordinates in first row
    data_matrix[1:, 0] = y  # Y coordinates in first column
    data_matrix[1:, 1:] = Z  # Z data

    data = pd.DataFrame(data_matrix)

    config = {
        "title": "Test 3D Plot",
        "x_label": "X Values",
        "y_label": "Y Values",
        "z_label": "Z Values"
    }

    plot_window = tk.Toplevel()
    app = Plot3DWindow(plot_window, data, config)

    root.mainloop()

if __name__ == "__main__":
    test_3d_plotting()
