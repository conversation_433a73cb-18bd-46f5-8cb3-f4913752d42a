#!/usr/bin/env python3
"""
Test to reproduce the specific PhotoImage issue
"""

import tkinter as tk
from tkinter import ttk
import os
import tempfile

try:
    from PIL import Image, ImageTk, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def test_modal_photoimage():
    """Test PhotoImage in modal window"""
    print("Testing PhotoImage in modal window...")
    
    # Create main window
    root = tk.Tk()
    root.title("Main Window")
    root.geometry("300x200")
    
    def open_modal():
        # Create modal window like ProjectOverview
        modal = tk.Toplevel(root)
        modal.title("Modal Test")
        modal.geometry("400x300")
        modal.transient(root)
        modal.grab_set()  # This might be the issue
        
        # Create canvas
        canvas = tk.Canvas(modal, bg="white", width=300, height=200)
        canvas.pack(pady=20)
        
        # Create test image
        if PIL_AVAILABLE:
            test_image = Image.new('RGB', (150, 100), color='red')
            draw = ImageDraw.Draw(test_image)
            draw.text((30, 40), "Modal Test", fill='white')
            
            # Test PhotoImage in modal context
            try:
                photo = ImageTk.PhotoImage(test_image)
                canvas.create_image(10, 10, anchor="nw", image=photo)
                
                # Keep reference to prevent garbage collection
                modal.photo = photo
                
                print("Modal PhotoImage created successfully!")
                
            except Exception as e:
                print(f"Modal PhotoImage error: {e}")
                canvas.create_text(10, 10, anchor="nw", text=f"Error: {e}")
        else:
            canvas.create_text(10, 10, anchor="nw", text="PIL not available")
    
    def open_non_modal():
        # Create non-modal window like DataAnalysisApp does
        non_modal = tk.Toplevel(root)
        non_modal.title("Non-Modal Test")
        non_modal.geometry("400x300")
        # No transient or grab_set
        
        # Create canvas
        canvas = tk.Canvas(non_modal, bg="white", width=300, height=200)
        canvas.pack(pady=20)
        
        # Create test image
        if PIL_AVAILABLE:
            test_image = Image.new('RGB', (150, 100), color='green')
            draw = ImageDraw.Draw(test_image)
            draw.text((20, 40), "Non-Modal Test", fill='white')
            
            # Test PhotoImage in non-modal context
            try:
                photo = ImageTk.PhotoImage(test_image)
                canvas.create_image(10, 10, anchor="nw", image=photo)
                
                # Keep reference to prevent garbage collection
                non_modal.photo = photo
                
                print("Non-modal PhotoImage created successfully!")
                
            except Exception as e:
                print(f"Non-modal PhotoImage error: {e}")
                canvas.create_text(10, 10, anchor="nw", text=f"Error: {e}")
        else:
            canvas.create_text(10, 10, anchor="nw", text="PIL not available")
    
    # Buttons to test both cases
    ttk.Button(root, text="Test Modal PhotoImage", command=open_modal).pack(pady=10)
    ttk.Button(root, text="Test Non-Modal PhotoImage", command=open_non_modal).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_modal_photoimage()
