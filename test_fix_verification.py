#!/usr/bin/env python3
"""
Quick test to verify the PhotoImage fix
"""

import tkinter as tk
from tkinter import ttk
try:
    from PIL import Image, ImageTk, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def test_photoimage_fix():
    """Test the PhotoImage fix"""
    
    if not PIL_AVAILABLE:
        print("PIL not available")
        return
    
    # Create window like ProjectOverview does when parent=None
    window = tk.Tk()
    window.title("PhotoImage Fix Test")
    window.geometry("500x400")
    
    # Create canvas
    canvas = tk.Canvas(window, bg="white", width=400, height=300)
    canvas.pack(pady=20)
    
    # Create test image
    test_image = Image.new('RGB', (200, 150), color='lightgreen')
    draw = ImageDraw.Draw(test_image)
    draw.text((50, 70), "Fix Test", fill='black')
    
    def test_without_master():
        """Test PhotoImage without master (old way)"""
        try:
            photo = ImageTk.PhotoImage(test_image)  # No master specified
            canvas.create_image(10, 10, anchor="nw", image=photo)
            canvas.photo1 = photo  # Keep reference
            print("SUCCESS: PhotoImage without master worked!")
        except Exception as e:
            print(f"ERROR: PhotoImage without master failed: {e}")
    
    def test_with_master():
        """Test PhotoImage with master (new way)"""
        try:
            photo = ImageTk.PhotoImage(test_image, master=window)  # With master
            canvas.create_image(220, 10, anchor="nw", image=photo)
            canvas.photo2 = photo  # Keep reference
            print("SUCCESS: PhotoImage with master worked!")
        except Exception as e:
            print(f"ERROR: PhotoImage with master failed: {e}")
    
    # Buttons to test both methods
    ttk.Button(window, text="Test Without Master (Old)", command=test_without_master).pack(pady=5)
    ttk.Button(window, text="Test With Master (New Fix)", command=test_with_master).pack(pady=5)
    
    # Status label
    status_label = ttk.Label(window, text="Click buttons to test PhotoImage methods")
    status_label.pack(pady=10)
    
    window.mainloop()

if __name__ == "__main__":
    test_photoimage_fix()
