#!/usr/bin/env python3
"""
Test script to debug ProjectOverview preview issue
"""

import sys
import os

# Test 1: Import and run ProjectOverview standalone
print("Testing ProjectOverview standalone...")
try:
    from ProjectOverview import ProjectOverviewWindow
    import tkinter as tk
    
    # Create a root window (non-modal case like DataAnalysisApp)
    root = tk.Tk()
    root.withdraw()  # Hide it like DataAnalysisApp does
    
    # Get default project directory
    import tempfile
    test_project_dir = tempfile.mkdtemp(prefix="test_projects_")
    print(f"Using test project directory: {test_project_dir}")
    
    # Create test project structure with preview image
    test_project_path = os.path.join(test_project_dir, "TestProject")
    test_measurement_path = os.path.join(test_project_path, "TestMeasurement")
    os.makedirs(test_measurement_path, exist_ok=True)
    
    # Create a test preview image
    try:
        from PIL import Image, ImageDraw
        test_image = Image.new('RGB', (200, 150), color='lightblue')
        draw = ImageDraw.Draw(test_image)
        draw.text((50, 70), "Test Preview", fill='black')
        
        preview_path = os.path.join(test_measurement_path, "TestMeasurement_preview.png")
        test_image.save(preview_path)
        print(f"Created test preview image: {preview_path}")
        
        # Create a plot_config.json file
        import json
        config = {"test": "config"}
        config_path = os.path.join(test_measurement_path, "plot_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f)
        print(f"Created test config: {config_path}")
        
    except ImportError:
        print("PIL not available - cannot create test image")
    
    # Create ProjectOverview window with None parent (like DataAnalysisApp does)
    print("Creating ProjectOverview window with None parent...")
    overview = ProjectOverviewWindow(None, test_project_dir, None)
    
    print("ProjectOverview window created successfully!")
    print("You should see the window. Try clicking on TestProject -> TestMeasurement to test preview.")
    
    # Run the main loop
    overview.window.mainloop()
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
