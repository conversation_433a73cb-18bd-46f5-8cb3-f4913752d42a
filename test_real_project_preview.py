#!/usr/bin/env python3
"""
Test ProjectOverview with real project directory
"""

import os
import json
from ProjectOverview import ProjectOverviewWindow
import tkinter as tk

try:
    from PIL import Image, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def create_test_project():
    """Create a test project with preview image in the real project directory"""
    
    # Read project directory from config
    try:
        with open('app_config.json', 'r') as f:
            config = json.load(f)
        project_dir = config.get('project_directory', 'C:\\Users\\<USER>\\Documents\\PlottingApp_Projects')
    except:
        project_dir = 'C:\\Users\\<USER>\\Documents\\PlottingApp_Projects'
    
    print(f"Using project directory: {project_dir}")
    
    # Create project directory if it doesn't exist
    os.makedirs(project_dir, exist_ok=True)
    
    # Create test project structure
    test_project = os.path.join(project_dir, "TestProject_PhotoImage")
    test_measurement = os.path.join(test_project, "TestMeasurement")
    os.makedirs(test_measurement, exist_ok=True)
    
    # Create preview image
    if PIL_AVAILABLE:
        test_image = Image.new('RGB', (300, 200), color='lightblue')
        draw = ImageDraw.Draw(test_image)
        draw.text((50, 90), "Test Preview Image", fill='black')
        draw.rectangle([10, 10, 290, 190], outline='red', width=2)
        
        preview_path = os.path.join(test_measurement, "TestMeasurement_preview.png")
        test_image.save(preview_path)
        print(f"Created preview image: {preview_path}")
        
        # Create plot config
        config = {"test": "config", "title": "Test Plot"}
        config_path = os.path.join(test_measurement, "plot_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Created config: {config_path}")
        
        return project_dir, preview_path
    else:
        print("PIL not available")
        return project_dir, None

def test_project_overview():
    """Test ProjectOverview with real project"""
    
    project_dir, preview_path = create_test_project()
    
    if not preview_path:
        print("Could not create test preview image")
        return
    
    print("\n" + "="*50)
    print("Testing ProjectOverview with real project directory")
    print("="*50)
    
    # Test 1: Non-modal (like DataAnalysisApp.py does)
    print("\nTest 1: Non-modal window (like DataAnalysisApp.py)")
    root = tk.Tk()
    root.withdraw()  # Hide like DataAnalysisApp does
    
    try:
        # Create ProjectOverview with None parent (non-modal)
        overview = ProjectOverviewWindow(None, project_dir, None)
        print("Non-modal ProjectOverview created successfully!")
        print("Look for TestProject_PhotoImage -> TestMeasurement and click it")
        print("Check console for debug output...")
        
        overview.window.mainloop()
        
    except Exception as e:
        print(f"Error creating non-modal ProjectOverview: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_project_overview()
